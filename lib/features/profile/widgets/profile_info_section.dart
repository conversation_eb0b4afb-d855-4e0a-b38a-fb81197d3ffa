import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../core/theme/app_colors.dart';
import '../models/profile_model.dart';

/// Widget displaying detailed profile information
class ProfileInfoSection extends StatelessWidget {
  /// The profile model to display
  final ProfileModel profile;

  const ProfileInfoSection({super.key, required this.profile});

  /// Format date for display
  String _formatDate(DateTime? date) {
    if (date == null) return 'Not set';
    
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec',
    ];
    
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Text(
          'Profile Information',
          style: theme.textTheme.titleMedium?.copyWith(
            color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
            fontWeight: FontWeight.w600,
          ),
        ),
        
        SizedBox(height: 16.h),
        
        // Info container
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: isDark ? AppColors.borderDark : AppColors.borderLight,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Basic Information
              _InfoRow(
                icon: Symbols.person,
                label: 'Full Name',
                value: profile.fullName,
              ),
              
              SizedBox(height: 12.h),
              
              _InfoRow(
                icon: Symbols.email,
                label: 'Email',
                value: profile.email,
              ),
              
              if (profile.phoneNumber != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.phone,
                  label: 'Phone',
                  value: profile.phoneNumber!,
                ),
              ],
              
              // Academic Information
              if (profile.grade != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.school,
                  label: 'Grade',
                  value: profile.grade!,
                ),
              ],
              
              if (profile.school != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.domain,
                  label: 'School',
                  value: profile.school!,
                ),
              ],
              
              if (profile.studentId != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.badge,
                  label: 'Student ID',
                  value: profile.studentId!,
                ),
              ],
              
              // Classes
              if (profile.classIds.isNotEmpty) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.groups,
                  label: 'Classes',
                  value: '${profile.classIds.length} enrolled',
                ),
              ],
              
              // Subjects
              if (profile.subjects.isNotEmpty) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.book,
                  label: 'Subjects',
                  value: profile.subjects.join(', '),
                ),
              ],
              
              // Personal Information
              if (profile.dateOfBirth != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.cake,
                  label: 'Date of Birth',
                  value: _formatDate(profile.dateOfBirth),
                ),
              ],
              
              if (profile.address != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.location_on,
                  label: 'Address',
                  value: profile.address!,
                ),
              ],
              
              // Emergency Contact
              if (profile.emergencyContact != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.emergency,
                  label: 'Emergency Contact',
                  value: profile.emergencyContact!,
                ),
              ],
              
              if (profile.emergencyContactPhone != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.emergency,
                  label: 'Emergency Phone',
                  value: profile.emergencyContactPhone!,
                ),
              ],
              
              // Parent/Guardian Information (for students)
              if (profile.parentGuardianName != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.family_restroom,
                  label: 'Parent/Guardian',
                  value: profile.parentGuardianName!,
                ),
              ],
              
              if (profile.parentGuardianPhone != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.family_restroom,
                  label: 'Parent Phone',
                  value: profile.parentGuardianPhone!,
                ),
              ],
              
              if (profile.parentGuardianEmail != null) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: Symbols.family_restroom,
                  label: 'Parent Email',
                  value: profile.parentGuardianEmail!,
                ),
              ],
              
              // Bio
              if (profile.bio != null && profile.bio!.isNotEmpty) ...[
                SizedBox(height: 16.h),
                Text(
                  'About',
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  profile.bio!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
                    height: 1.4,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}

/// Widget for displaying an information row
class _InfoRow extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color? valueColor;

  const _InfoRow({
    required this.icon,
    required this.label,
    required this.value,
    this.valueColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Row(
      children: [
        Icon(
          icon,
          size: 20.sp,
          color: isDark
              ? AppColors.textSecondaryDark
              : AppColors.textSecondaryLight,
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: isDark
                      ? AppColors.textSecondaryDark
                      : AppColors.textSecondaryLight,
                ),
              ),
              Flexible(
                child: Text(
                  value,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: valueColor ??
                        (isDark
                            ? AppColors.textPrimaryDark
                            : AppColors.textPrimaryLight),
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
